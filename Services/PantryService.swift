import Foundation
import SwiftUI
import SwiftData

@Observable
@MainActor
class PantryService {
    var pantryItems: [Ingredient] = []
    var recentlyAddedItems: Set<UUID> = []
    // Removed: private let nameNormalizer = IngredientNameNormalizer()
    private let canonicalizer = NameCanonicalizer()

    private let storageService = SwiftDataStorageService.shared

    // Task for clearing recently added highlights; allows cancellation/reset on new requests
    private var recentClearTask: Task<Void, Never>? = nil
    private var initialLoadTask: Task<Void, Never>?
    private var hasLoadedInitialPantry = false

    init() {
        initialLoadTask = Task { [weak self] in
            await self?.runInitialLoad()
        }
    }

    func waitUntilLoaded() async {
        if hasLoadedInitialPantry { return }

        if let task = initialLoadTask {
            await task.value
            initialLoadTask = nil
            return
        }

        let task = Task { [weak self] in
            await self?.runInitialLoad()
        }
        initialLoadTask = task
        await task.value
        initialLoadTask = nil
    }

    private func runInitialLoad() async {
        guard hasLoadedInitialPantry == false else { return }
        await storageService.waitUntilReady()
        await resetPantryIfFirstLaunch()
        await loadPantryItems()
        hasLoadedInitialPantry = true
        initialLoadTask = nil
    }

    func addIngredients(_ newIngredients: [Ingredient]) async {
        // Allow duplicates: always insert new items; do not dedupe or merge here.
        var itemsToInsert: [Ingredient] = []

        for ingredient in newIngredients {
            var newItem = ingredient
            // Ensure dates/cycle are set for newly created items
            newItem.dateAdded = Date()
            newItem.purchaseDate = newItem.dateAdded
            newItem.notificationCycle = 0
            pantryItems.append(newItem)
            itemsToInsert.append(newItem)
        }

        // Persist storage changes
        do {
            if !itemsToInsert.isEmpty {
                try await storageService.saveIngredients(itemsToInsert)
            }
        } catch {
            print("❌ Failed to persist ingredients: \(error)")
        }
    }

    /// Phase 2: Add-or-Replace with canonicalization and batch deduplication
    /// - For each input ingredient, canonicalize its name and within the same category:
    ///   - If a matching item exists (same canonical name), update it and reset dates/cycle
    ///   - Otherwise insert a new item
    /// - Deduplicate inputs that normalize to the same (name, category) in one call (first wins)
    /// - Persist atomically (all-or-nothing)
    func addOrReplaceIngredients(_ newIngredients: [Ingredient]) async {
        guard !newIngredients.isEmpty else { return }

        // Step 1: Prepare actions with canonical names and batch de-duplication (first wins)
        struct PreparedItem { let original: Ingredient; let canonicalName: String; let key: String }
        var seenKeys = Set<String>()
        var prepared: [PreparedItem] = []
        for ing in newIngredients {
            let canon = canonicalizer.canonicalize(ing.name)
            let key = "\(ing.category.rawValue.lowercased())|\(canon.lowercased())"
            if !seenKeys.contains(key) {
                seenKeys.insert(key)
                prepared.append(PreparedItem(original: ing, canonicalName: canon, key: key))
            }
        }
        guard !prepared.isEmpty else { return }

        // Step 2: Perform atomic upsert in storage
        do {
            let impactedIDs: [UUID] = try await storageService.performTransaction { ctx in
                // Load current SavedIngredient rows
                let existing: [SavedIngredient]
                do {
                    existing = try ctx.fetch(FetchDescriptor<SavedIngredient>())
                } catch {
                    throw error
                }

                // Build lookup by canonical key from existing storage
                func keyForSaved(_ s: SavedIngredient) -> String {
                    let canon = canonicalizer.canonicalize(s.name)
                    return "\(s.category.lowercased())|\(canon.lowercased())"
                }
                var byKey: [String: SavedIngredient] = [:]
                byKey.reserveCapacity(existing.count)
                for s in existing { byKey[keyForSaved(s)] = s }

                var changed: [UUID] = []
                let now = Date()

                for item in prepared {
                    if let match = byKey[item.key] {
                        // Replace behavior: update fields and reset dates/cycle
                        match.name = item.canonicalName
                        match.dateAdded = now
                        match.isRecent = true
                        match.notificationCycle = 0
                        changed.append(match.id)
                    } else {
                        // Insert new
                        let saved = SavedIngredient(
                            id: UUID(),
                            name: item.canonicalName,
                            category: item.original.category.rawValue,
                            dateAdded: now,
                            expiryDate: nil,
                            quantity: nil,
                            notes: nil,
                            isRecent: true,
                            notificationCycle: 0
                        )
                        ctx.insert(saved)
                        changed.append(saved.id)
                        // Track key to avoid another insert in same transaction if later prepared hits same key
                        byKey[item.key] = saved
                    }
                }
                return changed
            }

            // Step 3: Reload pantry from authoritative storage and highlight impacted items
            do {
                let savedIngredients = try storageService.fetchAllIngredients()
                pantryItems = savedIngredients.map { saved in
                    let migratedCategory = PantryCategoryMigration.mapOldCategoryToNew(saved.category, ingredientName: saved.name)
                    return Ingredient(
                        id: saved.id,
                        name: saved.name,
                        category: migratedCategory,
                        dateAdded: saved.dateAdded,
                        purchaseDate: saved.dateAdded,
                        notificationCycle: saved.notificationCycle
                    )
                }
                // Mark recently added (use IDs to ensure replaced items are highlighted)
                let impacted = pantryItems.filter { impactedIDs.contains($0.id) }
                markAsRecentlyAdded(impacted)
            } catch {
                print("❌ Failed to reload pantry after transaction: \(error)")
            }
        } catch {
            print("❌ PantryService addOrReplaceIngredients failed (rolled back): \(error)")
        }
    }

    func markAsRecentlyAdded(_ ingredients: [Ingredient]) {
        // Cancel any pending clear tasks to prevent race conditions
        recentClearTask?.cancel()
        recentClearTask = nil

        // Clear previous recently added items
        recentlyAddedItems.removeAll()
        // Mark new items as recently added
        for ingredient in ingredients {
            // Prefer ID match when the inserted item is new and IDs align
            if let pantryItem = pantryItems.first(where: { $0.id == ingredient.id }) {
                recentlyAddedItems.insert(pantryItem.id)
                continue
            }
            // Fallback: if add merged with an existing item (same name/category),
            // highlight that existing pantry item instead.
            let targetName = ingredient.name.trimmingCharacters(in: .whitespacesAndNewlines).lowercased()
            if let merged = pantryItems.first(where: {
                $0.category == ingredient.category &&
                $0.name.trimmingCharacters(in: .whitespacesAndNewlines).lowercased() == targetName
            }) {
                recentlyAddedItems.insert(merged.id)
            }
        }

        // Phase 1 Fix: Remove auto-clear timer - highlights persist while user stays in Pantry tab
        // Clearing now happens only when user switches away from Pantry tab
    }

    func addIngredient(_ ingredient: Ingredient) async {
        // Phase 2: route single adds through add-or-replace path
        await addOrReplaceIngredients([ingredient])
    }


    /// Merge any pre-existing duplicates in-memory and in storage.
    /// Duplicates are defined as same (trimmed, case-insensitive) name within the same category.
    /// Keeps the item with the most recent dateAdded and removes the rest.
    private func mergeDuplicates() async {
        var keepForKey: [String: Ingredient] = [:]
        var idsToDelete = Set<UUID>()

        for item in pantryItems {
            let key = "\(item.category.rawValue.lowercased())|\(item.name.trimmingCharacters(in: .whitespacesAndNewlines).lowercased())"
            if let existing = keepForKey[key] {
                // Keep the newer one by date
                if item.dateAdded > existing.dateAdded {
                    idsToDelete.insert(existing.id)
                    keepForKey[key] = item
                } else {
                    idsToDelete.insert(item.id)
                }
            } else {
                keepForKey[key] = item
            }
        }

        guard !idsToDelete.isEmpty else { return }

        // Remove duplicates in memory
        pantryItems.removeAll { idsToDelete.contains($0.id) }

        // Remove duplicates from storage
        for id in idsToDelete {
            if let ingredient = keepForKey.values.first(where: { $0.id == id }) {
                await deleteIngredientFromStorage(ingredient)
            } else {
                // Construct a lightweight Ingredient with the ID to delete from storage
                // (deleteIngredientFromStorage finds by id in SavedIngredient)
                await deleteIngredientFromStorage(Ingredient(id: id, name: "", category: .other))
            }
        }
    }

    func deletePantryItem(at offsets: IndexSet) async {
        let itemsToDelete = offsets.map { pantryItems[$0] }
        pantryItems.remove(atOffsets: offsets)

        // Delete from SwiftData storage
        for ingredient in itemsToDelete {
            await deleteIngredientFromStorage(ingredient)
        }
    }

    func deleteIngredient(_ ingredient: Ingredient) async {
        pantryItems.removeAll { $0.id == ingredient.id }
        await deleteIngredientFromStorage(ingredient)
    }

    func isRecentlyAdded(_ ingredient: Ingredient) -> Bool {
        return recentlyAddedItems.contains(ingredient.id)
    }

    /// Clear recently added highlights immediately (used when leaving Pantry tab)
    func clearRecentlyAddedHighlights() async {
        // Cancel any pending clear task and clear immediately
        recentClearTask?.cancel()
        recentClearTask = nil
        recentlyAddedItems.removeAll()
        do {
            try await storageService.clearRecentIngredients()
        } catch {
            print("❌ Failed to clear recent flags: \(error)")
        }
    }

    func updateIngredient(_ ingredient: Ingredient, newName: String, newCategory: PantryCategory) async {
        if let index = pantryItems.firstIndex(where: { $0.id == ingredient.id }) {
            pantryItems[index].name = newName
            pantryItems[index].category = newCategory

            // Update in SwiftData storage
            do {
                // Delete old and save updated
                await deleteIngredientFromStorage(ingredient)
                try await storageService.saveIngredient(pantryItems[index])
            } catch {
                print("❌ Failed to update ingredient in storage: \(error)")
            }
        }
    }

    /// Update ingredient by ID - used by PantryOrganizerService
    func updateIngredient(id: UUID, newName: String, newCategory: PantryCategory) async {
        if let index = pantryItems.firstIndex(where: { $0.id == id }) {
            pantryItems[index].name = newName
            pantryItems[index].category = newCategory

            // Update in SwiftData storage
            do {
                // Delete old and save updated
                let oldIngredient = pantryItems[index]
                await deleteIngredientFromStorage(oldIngredient)
                try await storageService.saveIngredient(pantryItems[index])
            } catch {
                print("❌ Failed to update ingredient in storage: \(error)")
            }
        }
    }

    /// Delete ingredient by ID - used by PantryOrganizerService
    func deleteIngredient(id: UUID) async {
        if let ingredient = pantryItems.first(where: { $0.id == id }) {
            await deleteIngredient(ingredient)
        }
    }

    /// Check if ingredient exists by ID - used by PantryOrganizerService for validation
    func ingredientExists(id: UUID) async -> Bool {
        return pantryItems.contains { $0.id == id }
    }

    func deleteIngredients(_ ingredients: [Ingredient]) async {
        for ingredient in ingredients {
            await deleteIngredient(ingredient)
        }
    }

    /// Delete ingredients by explicit ID set to avoid accidental mass deletion
    func deleteIngredients(withIDs ids: Set<UUID>) async {
        // 🛡️ Safety Check 1: Validate input
        guard !ids.isEmpty else {
            print("⚠️ PantryService: No IDs provided for deletion")
            return
        }

        // 🛡️ Safety Check 2: Prevent accidental mass deletion
        let totalItems = pantryItems.count
        let requestedDeletions = ids.count
        // 🛡️ More reasonable threshold: only prevent if deleting 80% or more, or more than 20 items
        let maxDeletionThreshold = min(max(1, Int(Double(totalItems) * 0.8)), 20)

        print("🔍 PantryService DEBUG: Total items: \(totalItems), Requested deletions: \(requestedDeletions)")

        if requestedDeletions > maxDeletionThreshold {
            print("🚨 PantryService SAFETY: Preventing mass deletion of \(requestedDeletions) items (threshold: \(maxDeletionThreshold))")
            return
        }

        // 🔄 Perform deletion
        print("🗑️ PantryService: Deleting \(requestedDeletions) ingredients")
        pantryItems.removeAll { ids.contains($0.id) }

        // 💾 Persist authoritative state
        do {
            try await storageService.replaceAllIngredients(with: pantryItems)
            print("✅ PantryService: Deletion completed and persisted")
        } catch {
            print("❌ PantryService: Failed to persist after deletion: \(error)")
        }
    }

    // 🔥 SIMPLE DELETE METHOD: No safety checks, just delete what user wants
    func deleteIngredientsSimple(withIDs ids: Set<UUID>) async {
        guard !ids.isEmpty else { return }

        print("🔥 PantryService SIMPLE: Deleting \(ids.count) ingredients - no safety checks")

        // Just delete the items
        pantryItems.removeAll { ids.contains($0.id) }

        // Save authoritative snapshot to storage
        do {
            try await storageService.replaceAllIngredients(with: pantryItems)
            print("✅ PantryService SIMPLE: Deletion completed")
        } catch {
            print("❌ PantryService SIMPLE: Failed to save after deletion: \(error)")
        }
    }


    // MARK: - First Launch Reset
    private func resetPantryIfFirstLaunch() async {
        let key = "has_cleared_pantry_on_first_launch_v1"
        let defaults = UserDefaults.standard
        guard defaults.bool(forKey: key) == false else { return }

        do {
            try await storageService.clearAllIngredients()
            await MainActor.run { pantryItems.removeAll() }
            defaults.set(true, forKey: key)
            print("🧹 Cleared pantry on first launch")
        } catch {
            print("❌ Failed to clear pantry on first launch: \(error)")
        }
    }


    // MARK: - Private Storage Methods

    private func loadPantryItems() async {
        do {
            let savedIngredients = try storageService.fetchAllIngredients()

            // Convert SavedIngredient to Ingredient, preserving persistent IDs
            pantryItems = savedIngredients.map { saved in
                let migratedCategory = PantryCategoryMigration.mapOldCategoryToNew(saved.category, ingredientName: saved.name)
                return Ingredient(
                    id: saved.id,
                    name: saved.name, // Use saved name as-is, no normalization
                    category: migratedCategory,
                    dateAdded: saved.dateAdded,
                    purchaseDate: saved.dateAdded,
                    notificationCycle: saved.notificationCycle
                )
            }

            // Mark recent items
            let recentItems = savedIngredients.filter { $0.isRecent }
            recentlyAddedItems = Set(recentItems.map { $0.id })

            print("✅ Loaded \(pantryItems.count) pantry items from storage")
        } catch {
            print("❌ Failed to load pantry items: \(error)")
        }
    }

    private func deleteIngredientFromStorage(_ ingredient: Ingredient) async {
        do {
            let savedIngredients = try storageService.fetchAllIngredients()
            if let savedIngredient = savedIngredients.first(where: { $0.id == ingredient.id }) {
                try await storageService.deleteIngredient(savedIngredient)
            }
        } catch {
            print("❌ Failed to delete ingredient from storage: \(error)")
        }
    }

    /// Update notificationCycle for a set of ingredients and persist the change
    func updateNotificationCycles(_ updates: [UUID: Int]) async {
        guard !updates.isEmpty else { return }
        // Update in-memory models
        for index in pantryItems.indices {
            let id = pantryItems[index].id
            if let newCycle = updates[id] {
                pantryItems[index].notificationCycle = newCycle
            }
        }
        // Persist authoritative snapshot
        do {
            try await storageService.replaceAllIngredients(with: pantryItems)
            print("✅ Updated notification cycles for \(updates.count) items")
        } catch {
            print("❌ Failed to persist notification cycle updates: \(error)")
        }
    }
}

// MARK: - Category Migration

enum PantryCategoryMigration {
    static func mapOldCategoryToNew(_ raw: String, ingredientName: String) -> PantryCategory {
        let trimmed = raw.trimmingCharacters(in: .whitespacesAndNewlines)
        switch trimmed {
        case PantryCategory.bakingAndSweeteners.rawValue:
            return .bakingAndSweeteners
        case PantryCategory.oilsVinegarsAndCondiments.rawValue:
            return .oilsVinegarsAndCondiments
        case PantryCategory.spicesAndSeasonings.rawValue:
            return .spicesAndSeasonings
        case "Dry Goods":
            if IngredientNameNormalizer.isNutOrSeed(ingredientName) { return .nutsAndSeeds }
            return .grainsPastaLegumes
        case "Packaged Foods":
            return .cannedAndBroths
        case "Dairy & Alternatives":
            if IngredientNameNormalizer.isPlantBasedAlternative(ingredientName) { return .plantBasedAlternatives }
            return .dairy
        case "Pastry":
            return .bakery
        case "Snacks & Beverages":
            return .snacks
        case PantryCategory.proteins.rawValue:
            return .proteins
        case PantryCategory.produce.rawValue:
            return .produce
        default:
            return PantryCategory(rawValue: trimmed) ?? .other
        }
    }
}

// MARK: - Name Normalization

struct IngredientNameNormalizer {
    func normalize(_ name: String) -> String {
        let lower = name.lowercased().replacingOccurrences(of: " ", with: "")
        // Examples: unify diced tomatoes variants
        if lower.contains("dicedtomato") || lower.contains("切丁番茄") {
            return "canned tomatoes"
        }
        return canonicalLookup[name.lowercased()] ?? name.trimmingCharacters(in: .whitespacesAndNewlines)
    }
    static func isNutOrSeed(_ name: String) -> Bool {
        let n = name.lowercased()
        return ["almond","walnut","pecan","pistachio","cashew","peanut","hazelnut","chia","flax","sesame","sunflower","pumpkin"].contains { n.contains($0) }
    }
    static func isPlantBasedAlternative(_ name: String) -> Bool {
        let n = name.lowercased()
        return ["oat milk","almond milk","soy milk","coconut milk","rice milk","vegan","plant-based","tofu","tempeh","seitan","tvp"].contains { n.contains($0) }
    }
}

private let canonicalLookup: [String: String] = {
    // Minimal seed; can be expanded or loaded from file later
    [
        "dicedtomatoes": "canned tomatoes",
        "diced tomatoes": "canned tomatoes",
        "一罐切丁番茄": "canned tomatoes"
    ]
}()
