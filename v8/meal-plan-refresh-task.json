{"version": "1.0", "document": "V8 Meal Plan Execution PRD", "last_updated": "2024-07-08", "objective": "Restore immediate visibility of structured meal plans and harden V8 generation pipelines without changing Quick mode behaviour.", "workstreams": [{"id": "WS-1", "name": "Plan Visibility Fix", "owners": ["Recipes pod"], "tasks": [{"task_id": "WS-1-T1", "description": "Emit planStoreDidChange notification after merge/save on MainActor.", "acceptance": ["Notification posted once per successful save", "Notification delivery confirmed in unit test"], "related_requirements": ["FR-2.1"], "status": "pending"}, {"task_id": "WS-1-T2", "description": "Subscribe RecipesViewModel to PlanStore notifications and trigger reload().", "acceptance": ["Observer lifecycle tied to view-model deinit", "Reload runs on MainActor"], "related_requirements": ["FR-2.2"], "status": "pending"}, {"task_id": "WS-1-T3", "description": "Add PlansHistoryView.onAppear reload guard and regression test for immediate visibility.", "acceptance": ["View reloads on navigation", "UI/regression test passes without app relaunch"], "related_requirements": ["FR-2.3", "FR-2.5"], "status": "pending"}]}, {"id": "WS-2", "name": "Pantry & Slot Validation", "owners": ["Generator pod"], "tasks": [{"task_id": "WS-2-T1", "description": "Implement pantry readiness wait and noPantryItems error mapping.", "acceptance": ["Pantry readiness awaited before generation", "DisplayError.pantryEmpty surfaced in UI"], "related_requirements": ["FR-3.1", "FR-3.2"], "status": "pending"}, {"task_id": "WS-2-T2", "description": "Detect empty slot enumerations and surface friendly cutoff messaging.", "acceptance": ["noEligibleSlots error thrown on empty slots", "User-facing message verified with Product"], "related_requirements": ["FR-3.3"], "status": "pending"}, {"task_id": "WS-2-T3", "description": "Add structured logging and counters for pantry/slot guardrails.", "acceptance": ["Counters visible in telemetry dashboard", "Logs tagged with meal-plan namespace"], "related_requirements": ["FR-3.4"], "status": "pending"}]}, {"id": "WS-3", "name": "Performance & Observability", "owners": ["Core iOS Platform"], "tasks": [{"task_id": "WS-3-T1", "description": "Maintain bounded TaskGroup parallelism and MainActor responsiveness.", "acceptance": ["Concurrency cap enforced in code", "Cancel latency < 500 ms measured"], "related_requirements": ["FR-4.2", "FR-4.3"], "status": "pending"}, {"task_id": "WS-3-T2", "description": "Offload PlanStore merge/encode work off MainActor and keep atomic writes.", "acceptance": ["Background merge verified in profiler", "MainActor write remains atomic"], "related_requirements": ["FR-4.4"], "status": "pending"}, {"task_id": "WS-3-T3", "description": "Execute integration tests for performance scenario and capture telemetry baselines.", "acceptance": ["Integration test automation in CI", "Baseline metrics stored for comparison"], "related_requirements": ["FR-4.5", "FR-5.1"], "status": "pending"}]}], "validation_plan": {"automated_tests": ["Fingerprints guard", "Prefetch flag", "Per-meal isolation", "Date indexing", "Timeout helper", "Plan visibility regression"], "manual_scenarios": ["Complex configuration", "Configuration change detection", "Overlap append", "Performance Lunch+Dinner", "Manage integration", "Start-date cutoff"]}, "risks": [{"id": "R-1", "description": "Notification observer leaks leading to duplicate reloads", "mitigation": "Use MainActor-bound observer removed on deinit"}, {"id": "R-2", "description": "Pantry readiness wait prolongs generation start", "mitigation": "Add timeout and telemetry to monitor wait duration"}, {"id": "R-3", "description": "Parallel generation saturates API", "mitigation": "Keep concurrency cap ≤3 and monitor ai_calls_total"}], "next_checkpoints": [{"name": "Sprint kickoff", "date": "2024-07-09", "exit_criteria": ["Owners confirmed", "Stories sized"]}, {"name": "Mid-sprint review", "date": "2024-07-12", "exit_criteria": ["WS-1 code complete", "QA smoke run"]}, {"name": "Release readiness", "date": "2024-07-15", "exit_criteria": ["All validation green", "Telemetry dashboards updated"]}]}